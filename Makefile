.PHONY:  docker-build compose-up-version

# Docker相关命令
docker-build:
	@echo "Building Docker image..."
	@read -p "请输入镜像版本 (例如: v1.0.0): " VERSION; \
	if [ -z "$$VERSION" ]; then \
		echo "版本不能为空"; \
		exit 1; \
	fi; \
	docker build -t zjl:$$VERSION -t zjl:latest .; \
	docker tag zjl:$$VERSION registry.rcztcs.com/zjl:$$VERSION; \
	docker tag zjl:latest registry.rcztcs.com/zjl:latest; \
	echo "Docker image build success: zjl:$$VERSION"; \
	echo "准备推送镜像到仓库..."; \
	docker push registry.rcztcs.com/zjl:$$VERSION; \
	docker push registry.rcztcs.com/zjl:latest; \
	echo "Docker image pushed: registry.rcztcs.com/zjl:$$VERSION"

compose-up-version:
	@echo "Starting services with specific version..."
	@read -p "请输入要启动的镜像版本 (例如: v1.0.0): " VERSION; \
	if [ -z "$$VERSION" ]; then \
		echo "版本不能为空"; \
		exit 1; \
	fi; \
	sed -i.bak "s|registry.rcztcs.com/zjl:.*|registry.rcztcs.com/zjl:$$VERSION|g" docker-compose.yaml; \
	docker-compose up -d; \
	mv docker-compose.yaml.bak docker-compose.yaml; \
	echo "Services started with version: $$VERSION"