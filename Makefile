.PHONY: build image docker-build docker-run docker-stop docker-clean compose-up compose-down compose-logs

all: image

gobuild:
	@sh scripts/gobuild.sh

web:
	@echo "web build success"

image: gobuild web
	@echo "image build success"

# Docker相关命令
docker-build:
	@echo "Building Docker image..."
	@docker build -t zjl:latest .
	@echo "Docker image build success"

docker-run:
	@echo "Starting Docker container..."
	@docker run -d --name zjl-app -p 8080:8080 \
		-v $(PWD)/uploads:/app/uploads \
		-v $(PWD)/config:/app/config \
		zjl:latest
	@echo "Docker container started"

docker-stop:
	@echo "Stopping Docker container..."
	@docker stop zjl-app || true
	@docker rm zjl-app || true
	@echo "Docker container stopped"

docker-clean: docker-stop
	@echo "Cleaning Docker images..."
	@docker rmi zjl:latest || true
	@echo "Docker cleanup complete"

# Docker Compose相关命令
compose-up:
	@echo "Starting services with docker-compose..."
	@docker-compose up -d
	@echo "Services started with docker-compose"

compose-down:
	@echo "Stopping services with docker-compose..."
	@docker-compose down
	@echo "Services stopped with docker-compose"

compose-logs:
	@echo "Showing docker-compose logs..."
	@docker-compose logs -f

# 重新构建并启动
rebuild: docker-clean docker-build docker-run

# 使用docker-compose重新构建并启动
compose-rebuild:
	@docker-compose down
	@docker-compose build --no-cache
	@docker-compose up -d
