# Docker相关命令
docker-build:
	@echo "Building Docker image..."
	@docker build -t zjl:latest .
	@echo "Docker image built"
# Docker Compose相关命令
compose-up:
	@echo "Starting services with docker-compose..."
	@docker-compose up -d
	@echo "Services started with docker-compose"

compose-down:
	@echo "Stopping services with docker-compose..."
	@docker-compose down
	@echo "Services stopped with docker-compose"

compose-logs:
	@echo "Showing docker-compose logs..."
	@docker-compose logs -f

# 重新构建并启动
rebuild: docker-clean docker-build docker-run

# 使用docker-compose重新构建并启动
compose-rebuild:
	@docker-compose down
	@docker-compose build --no-cache
	@docker-compose up -d
