version: '3.8'

services:
  zjl:
    build:
      context: .
      dockerfile: Dockerfile
    image: zjl:latest
    container_name: zjl-app
    ports:
      - "8080:8080"
    volumes:
      - ./uploads:/app/uploads
      - ./config:/app/config
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped
    networks:
      - zjl-network

  # 可选：如果需要MySQL数据库
  # mysql:
  #   image: mysql:8.0
  #   container_name: zjl-mysql
  #   environment:
  #     MYSQL_ROOT_PASSWORD: rootpassword
  #     MYSQL_DATABASE: zjl
  #     MYSQL_USER: zjluser
  #     MYSQL_PASSWORD: zjlpassword
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   networks:
  #     - zjl-network

  # 可选：如果需要MongoDB数据库
  # mongodb:
  #   image: mongo:7.0
  #   container_name: zjl-mongodb
  #   environment:
  #     MONGO_INITDB_ROOT_USERNAME: root
  #     MONGO_INITDB_ROOT_PASSWORD: rootpassword
  #     MONGO_INITDB_DATABASE: zjl
  #   ports:
  #     - "27017:27017"
  #   volumes:
  #     - mongodb_data:/data/db
  #   networks:
  #     - zjl-network

networks:
  zjl-network:
    driver: bridge

# volumes:
#   mysql_data:
#   mongodb_data:
