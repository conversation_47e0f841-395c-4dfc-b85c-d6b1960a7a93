# 第一阶段：构建阶段
FROM golang:1.24.3-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache git

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 编译Go程序
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o zjl ./cmd/zjl

# 第二阶段：运行阶段
FROM alpine:latest

# 安装ca证书和时区数据
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/zjl .

# 复制配置文件和其他必要文件
COPY --from=builder /app/config ./config
COPY --from=builder /app/frontend ./frontend

# 创建uploads目录
RUN mkdir -p uploads && \
    chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["./zjl"]
