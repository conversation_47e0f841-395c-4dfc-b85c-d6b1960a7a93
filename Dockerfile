# 构建阶段
FROM golang:1.24.5-alpine3.22e AS builder

WORKDIR /app
RUN apk add --no-cache git

COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o zjl ./cmd/zjl

# 运行阶段
FROM alpine:3.22

RUN apk --no-cache add ca-certificates tzdata
ENV TZ=Asia/Shanghai

RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

WORKDIR /app

COPY --from=builder /app/zjl .
COPY --from=builder /app/config ./config
COPY --from=builder /app/frontend ./frontend

RUN mkdir -p uploads && \
    chown -R appuser:appgroup /app

USER appuser
EXPOSE 8080
CMD ["./zjl"]
